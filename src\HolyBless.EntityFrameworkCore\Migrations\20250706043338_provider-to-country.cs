using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace HolyBless.Migrations
{
    /// <inheritdoc />
    public partial class providertocountry : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PreferCountry",
                table: "StorageProviders");

            migrationBuilder.AddColumn<string>(
                name: "DefaultLangCode",
                table: "Countries",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "DefaultSpokenLangCode",
                table: "Countries",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<int>(
                name: "StorageProviderId",
                table: "Countries",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SpokenLangCode",
                table: "Buckets",
                type: "character varying(3)",
                unicode: false,
                maxLength: 3,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "StorageProviderToCountries",
                columns: table => new
                {
                    StorageProviderId = table.Column<int>(type: "integer", nullable: false),
                    CountryId = table.Column<int>(type: "integer", nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StorageProviderToCountries", x => new { x.StorageProviderId, x.CountryId });
                    table.ForeignKey(
                        name: "FK_StorageProviderToCountries_Countries_CountryId",
                        column: x => x.CountryId,
                        principalTable: "Countries",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_StorageProviderToCountries_StorageProviders_StorageProvider~",
                        column: x => x.StorageProviderId,
                        principalTable: "StorageProviders",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_Countries_StorageProviderId",
                table: "Countries",
                column: "StorageProviderId");

            migrationBuilder.CreateIndex(
                name: "IX_StorageProviderToCountries_CountryId",
                table: "StorageProviderToCountries",
                column: "CountryId");

            migrationBuilder.AddForeignKey(
                name: "FK_Countries_StorageProviders_StorageProviderId",
                table: "Countries",
                column: "StorageProviderId",
                principalTable: "StorageProviders",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Countries_StorageProviders_StorageProviderId",
                table: "Countries");

            migrationBuilder.DropTable(
                name: "StorageProviderToCountries");

            migrationBuilder.DropIndex(
                name: "IX_Countries_StorageProviderId",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "DefaultLangCode",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "DefaultSpokenLangCode",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "StorageProviderId",
                table: "Countries");

            migrationBuilder.DropColumn(
                name: "SpokenLangCode",
                table: "Buckets");

            migrationBuilder.AddColumn<string>(
                name: "PreferCountry",
                table: "StorageProviders",
                type: "character varying(3)",
                unicode: false,
                maxLength: 3,
                nullable: true);
        }
    }
}
