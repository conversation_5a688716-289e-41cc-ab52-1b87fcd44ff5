using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using HolyBless.Lookups;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.DataSeeders
{
    public class StorageProviderDataSeederContributor : IDataSeedContributor, ITransientDependency
    {
        private readonly IRepository<StorageProvider, int> _storageProviderRepository;
        private readonly IRepository<Bucket, int> _bucketRepository;
        private readonly IRepository<Country, int> _countryRepository;

        public StorageProviderDataSeederContributor(
            IRepository<StorageProvider, int> storageProviderRepository
            , IRepository<Bucket, int> bucketRepository
            , IRepository<Country, int> countryRepository)
        {
            _storageProviderRepository = storageProviderRepository;
            _bucketRepository = bucketRepository;
            _countryRepository = countryRepository;
        }

        private static List<Country> GetCountry(List<Country> allCountries, string[] countryCode)
        {
            return allCountries.Where(c => countryCode.Contains(c.Code)).ToList();
        }

        public async Task SeedAsync(DataSeedContext context)
        {
            await DeleteExistingData();

            var allCountries = await _countryRepository.GetListAsync();

            var env = EnvironmentConst.Dev.ToString();
            var domain = "xieanshuo.com";

            await _storageProviderRepository.InsertAsync(new StorageProvider(1)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.China]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for US region."
            }, autoSave: true);

            await _storageProviderRepository.InsertAsync(new StorageProvider(2)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.UnitedStates]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for US region and all other region outside China."
            }, autoSave: true);

            await _storageProviderRepository.InsertAsync(new StorageProvider(3)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.China]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for China region."
            }, autoSave: true);

            await _storageProviderRepository.InsertAsync(new StorageProvider(4)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.HongKong, CountryCodeConstants.Macao]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for HongKong region."
            }, autoSave: true);

            await _storageProviderRepository.InsertAsync(new StorageProvider(5)
            {
                ProviderName = "Cloudflare R2",
                ProviderCode = ProviderCodeConstants.CloudFlare,
                PreferCountries = GetCountry(allCountries, [CountryCodeConstants.Taiwan]),
                Environment = env,
                BindedDomain = domain,
                Description = "Cloudflare R2 storage for Taiwan region."
            }, autoSave: true);

            var allProviders = await _storageProviderRepository.GetListAsync();
            foreach (var provider in allProviders)
            {
                var country = provider.PreferCountries.FirstOrDefault();
                if (country == null)
                {
                    country = allCountries.First(x => x.Code == "US");
                }
                await TaskSeedBucket(provider.Id, country.DefaultLangCode, country.DefaultSpokenLangCode);
            }
        }

        private async Task TaskSeedBucket(int providerId, string langCode, string spokenLangCode)
        {
            var buckets = new List<Bucket> {
                new() {
                    StorageProviderId = providerId,
                    BucketName = $"thumbnail-{langCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "dev-cf-img-zh-hans",
                    ContentType = ContentCategory.Thumbnail
                },
                new() {
                    StorageProviderId = providerId,
                    BucketName = $"images-{langCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "dev-cf-img-zh-hans",
                    ContentType = ContentCategory.Image
                },
                new ()
                {
                    StorageProviderId = providerId,
                    BucketName = $"docs-{langCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "dev-cf-doc-zh-hans",
                    ContentType = ContentCategory.Document
                },
                new ()
                {
                    StorageProviderId = providerId,
                    BucketName = $"package-{langCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "dev-cf-pkg",
                    ContentType = ContentCategory.Document
                },
                new ()
                {
                    StorageProviderId = providerId,
                    BucketName = $"orginal-videos-{langCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "dev-cf-ogvideo-zh-hans-cmn",
                    ContentType = ContentCategory.OriginalVideo
                },
                new ()
                {
                    StorageProviderId = providerId,
                    BucketName = $"non-orginal-videos-{langCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "dev-cf-notogvideo-zh-hans-cmn",
                    ContentType = ContentCategory.NonOriginalVideo
                },
                new ()
                {
                    StorageProviderId = providerId,
                    BucketName = $"orginal-audios-{langCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "dev-cf-notogaudio-cmn",
                    ContentType = ContentCategory.OriginalAudio
                },
                new ()
                {
                    StorageProviderId = providerId,
                    BucketName = $"non-orginal-audios-{langCode}",
                    LanguageCode = langCode,
                    SpokenLangCode = spokenLangCode,
                    SubDomain = "dev-cf-notogaudio-cmn",
                    ContentType = ContentCategory.NonOriginalAudio
                }
            };

            await _bucketRepository.InsertManyAsync(buckets, autoSave: true);
        }

        private async Task DeleteExistingData()
        {
            var existingAllBucket = await _bucketRepository.GetListAsync();
            if (await _bucketRepository.GetCountAsync() > 0)
            {
                await _bucketRepository.HardDeleteAsync(existingAllBucket, true);
            }
            var existingAll = await _storageProviderRepository.GetListAsync();
            if (await _storageProviderRepository.GetCountAsync() > 0)
            {
                await _storageProviderRepository.HardDeleteAsync(existingAll, true);
            }
        }
    }
}